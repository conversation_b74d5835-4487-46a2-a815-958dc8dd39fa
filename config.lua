Config = {}

Config.Notification = function(message, type)
    TriggerEvent('esx:showNotification', message, type, 2500)
end

Config.Items = {
    ['water'] = { -- Item
        category = "category_1", -- Category
        display = "Water", -- Display Name
        price = 50, -- Price per unit
        description = "A fresh tomato, great for your receipts...", -- Description
    },
    ['fanta'] = {
        category = "category_1",
        display = "Fanta Juice",
        price = 110,
        description = "Fanta with OPS..",
    },
    ['sprite'] = {
        category = "category_1",
        display = "Sprite",
        price = 110,
        description = "Sprite with OPS..",
    },
    ['vodka'] = {
        category = "category_1",
        display = "Vodka",
        price = 500,
        description = "Micheal Vodka..",
    },
    ['tequila'] = {
        category = "category_1",
        display = "Tequila",
        price = 200,
        description = "Fucking Tequila..",
    },
    ['cocacola'] = {
        category = "category_1",
        display = "CocaCola",
        price = 100,
        description = "Russian CocaCola IQ..",
    },
    ['burger'] = {
        category = "category_2",
        display = "Hamburger",
        price = 100,
        description = "A delicious hamburger...",
    },
    ['pizza'] = {
        category = "category_2",
        display = "Pizza",
        price = 150,
        description = "A delicious Pizza With Sus...",
    },
    ['bread'] = {
        category = "category_2",
        display = "Bread",
        price = 150,
        description = " Bread",
    },
    ['macka'] = {
        category = "category_2",
        display = "Sandwitch",
        price = 150,
        description = "A delicious Sandwitch With Sus...",
    },
    ['phone'] = {
        category = "category_3",
        display = "Phone",
        price = 1000,
        description ="Phone",
    },
    ['radio'] = {
        category = "category_3",
        display = "Radio",
        price = 2000,
        description =  "Radio",
    },
    ['lotteryticket'] = {
        category = "category_3",
        display = "Blit Bakht Azmayi",
        price = 1000,
        description =  "Blit Bakht Azmayi",
    },
}

Config.Locale = {
    buyed = "Kharid Shoma Ba Mablaq ~g~$%s~w~ Taiid Shod",
    nomoney = "~r~Shoma Pool Kafi Barai Kharid In Item Ra Nadarid",
    Interact = "Press ~INPUT_CONTEXT~ To Access The Shop",
}

Config.Shops = {
    { x = 373.875,   y = 325.896,   z = 102.566, blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 2557.458,  y = 382.282,   z = 107.622, blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -3038.939, y = 585.954,   z = 6.908,   blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -3241.927, y = 1001.462,  z = 11.830,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -2191.65,  y = 4285.4,    z = 49.18,   blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 1961.464,  y = 3740.672,  z = 31.343,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 2678.916,  y = 3280.671,  z = 54.241,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 1729.216,  y = 6414.131,  z = 34.037,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 1135.808,  y = -982.281,  z = 45.415,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 25.7,      y = -1346.7,   z = 28.4,    blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -1222.915, y = -906.983,  z = 11.326,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -1487.553, y = -379.107,  z = 39.163,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -2968.243, y = 390.910,   z = 14.043,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 1166.024,  y = 2708.930,  z = 37.157,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 1392.562,  y = 3604.684,  z = 33.980,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 127.830,   y = -1284.796, z = 28.280,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -1393.409, y = -606.624,  z = 29.319,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -559.906,  y = 287.093,   z = 81.176,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -48.519,   y = -1757.514, z = 28.421,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 1163.373,  y = -323.801,  z = 68.205,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -707.501,  y = -914.260,  z = 18.215,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = -1820.523, y = 792.518,   z = 137.118, blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 },
    { x = 1698.388,  y = 4924.404,  z = 41.063,  blipname = "Store", blipsprite = 59, blipcolor = 43, blipscale = 0.7, dist = 2.5 }
}