@import url('https://fonts.googleapis.com/css2?family=Baloo+Bhaijaan+2:wght@600&display=swap');
* {
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
}

body {
    font-family: 'Baloo Bhaijaan 2', cursive;
    font-weight: 400;
    color: #ffffff;
    font-size: 16px;
    line-height: 1.4;
    overflow-x: hidden;
    display: none;
}

a,
a:hover {
    color: inherit;
    cursor: pointer;
    text-decoration: none;
}

ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

img {
    max-width: 100%;
}

button {
    border: none;
    outline: none;
    box-shadow: none;
    cursor: pointer;
}


/* Chrome, Safari, Edge, Opera */

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

::placeholder {
    /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: #ffffff;
    opacity: 1;
    /* Firefox */
}

:-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #ffffff;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    color: #ffffff;
    transition: background-color 5000s ease-in-out 0s;
}

::-ms-input-placeholder {
    /* Microsoft Edge */
    color: #ffffff;
}


/* Firefox */

input[type="number"] {
    -moz-appearance: textfield;
}

textarea:focus,
input:focus {
    border: none;
    outline: none;
    box-shadow: none;
}

::selection {
    color: white;
    background: #fc033d;
}

::-webkit-selection {
    color: white;
    background: #fc033d;
}

::-moz-selection {
    color: white;
    background: #fc033d;
}

.main_area {
    position: relative;
    background-image: url(img/shop_bg.png);
    background-size: cover;
    background-position: bottom center;
    background-repeat: no-repeat;
    min-height: 100vh;
    width: 100%;
}

.container_box {
    max-width: 1640px;
    padding: 0px 150px;
    margin: 0 auto;
}

.main_row {
    display: flex;
    justify-content: space-between;
}

.main_right {
    width: 70%;
    position: relative;
    right: -40px;
}

.main_left {
    width: 34%;
    max-width: 300px;
}

.logo_box {
    padding-top: 60px;
    padding-bottom: 120px;
}

.logo_box h3 {
    position: relative;
    z-index: 9999;
    font-family: 'Baloo Bhaijaan 2', cursive;
    font-weight: bold;
    font-size: 48px;
    color: #ffffff;
    text-transform: uppercase;
    text-shadow: 0px 0px 79px rgba(255, 255, 255, 0.35);
}

.logo_box span {
    position: relative;
    z-index: 9999;
    font-family: 'Baloo Bhaijaan 2', cursive;
    font-size: 16px;
    display: block;
    font-weight: 400;
}

.logo_text {
    position: relative;
}

.logo_text img {
    max-width: 220px;
    width: 100%;
    position: absolute;
    top: -14px;
    left: -60px;
}

.cart_item_box {
    position: relative;
}

.cart_item_box ul li .cart_price {
    color: rgba(255, 255, 255, 0.65);
}

.cart_item_box ul li {
    position: relative;
    z-index: 9999;
    height: 55px;
    margin-bottom: 10px;
    font-weight: 600;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.06);
    border-radius: 8px;
}

.cart_item_box ul li .cart_items {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    padding: 12px 20px;
    height: 55px;
    margin-bottom: 10px;
    font-weight: 600;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    z-index: 999;
    border-radius: 8px;
    transition: 0.3s ease-in-out;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
}

.cart_item_box ul li .cart_items_number {
    position: absolute;
    bottom: -100%;
    left: 0px;
    width: 100%;
    height: 100%;
    padding: 12px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: -999;
    transition: 0.3s ease-in-out;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
}

.cart_item_box ul {
    padding-bottom: 20px;
}

.cart_item_box ul li:hover .cart_items_number {
    bottom: 0px;
    z-index: 999999;
    transition: 0.3s ease-in-out;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
}

.cart_item_box ul li:hover .cart_items {
    position: absolute;
    left: -100%;
    z-index: -999;
    transition: 0.3s ease-in-out;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
}

.cart_items .cart_text {
    position: relative;
}

.cart_items_number button {
    position: relative;
    z-index: 999999;
    background: transparent;
    border: none;
    font-size: 20px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    overflow: hidden;
}

.cart_items_number .cart_plus button,
.cart_items_number .cart_minus button {
    background: transparent;
    border: none;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart_items_number .cart_minus button {
    background: rgba(189, 101, 101, 0.15);
    color: rgba(252, 3, 61, 1);
}

.cart_items_number .cart_plus button {
    background: rgba(116, 189, 101, 0.15);
    color: rgba(3, 252, 115, 1);
}

.cart_items_number .title_cart {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 100%;
    height: 100%;
    color: rgba(255, 255, 255, 0.05);
    font-weight: bold;
    text-transform: uppercase;
    font-size: 24px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: default;
}

.cart_item_box .side_title {
    position: absolute;
    left: -210px;
    bottom: 78%;
    display: flex;
    transform: rotate(-90deg);
    width: 100%;
    height: 60px;
}

.cart_item_box .side_title h4 {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.cart_item_box .side_title svg {
    transform: rotate(90deg);
    margin-right: 20px;
    max-width: 26px;
}

.total_price_box {
    max-width: 186px;
    margin: 0px auto;
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.11);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 0px;
}

.total_price_box span {
    display: block;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
}

.total_price_box .total_price {
    color: rgba(3, 252, 115, 1);
    font-size: 20px;
}

.total_price_btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 240px;
    margin: 14px auto 0px;
    padding-bottom: 70px;
}

.product_content button,
.total_price_btns button {
    padding: 10px;
    height: 50px;
    max-width: 130px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(116, 189, 101, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.11);
    border-radius: 8px;
    overflow: hidden;
    font-weight: 600;
    font-size: 16px;
    text-transform: uppercase;
    color: rgba(3, 252, 115, 1);
    transition: 0.3s ease-in-out;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
}

.total_price_btns .clear_btn {
    max-width: 100px;
    width: 100%;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.11);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.55);
    transition: 0.3s ease-in-out;
    -webkit-transition: 0.3s ease-in-out;
    -moz-transition: 0.3s ease-in-out;
}

.total_price_btns .clear_btn:hover,
.total_price_btns button:hover {
    background: rgba(116, 189, 101, 0.6);
    color: #ffffff;
}

.product_box {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-wrap: wrap;
    padding-right: 10px;
    padding-bottom: 10px;
    /* height: 630px; */
    /* overflow-y: scroll; */
}

.product__box-container {
    max-height: 620px;
    overflow-y: auto;
    height: 100%;
}


/* Scroll Bar */


/* ===== Scrollbar CSS ===== */


/* Firefox */

.product__box-container {
    scrollbar-width: auto;
    scrollbar-color: rgba(255, 255, 255, 0.1);
}


/* Chrome, Edge, and Safari */

.product__box-container::-webkit-scrollbar {
    width: 4px;
}

.product__box-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.product__box-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border: 4px solid #ffffff;
}


/* Scroll Bar */

.product_btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 60px;
    padding-bottom: 70px;
}

.filter-button {
    width: 20%;
    font-family: 'Baloo Bhaijaan 2', cursive;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    color: rgba(255, 255, 255, 0.55);
    display: block;
    text-transform: uppercase;
    font-size: 15px;
    padding: 8px 26px 10px;
    font-weight: 600;
    border-bottom: 1.5px solid rgba(116, 185, 110, 0);
    min-width: 70px;
    margin: 0% 0.5%;
    overflow: hidden;
    transition: 0.25s ease-in-out;
    -webkit-transition: 0.25s ease-in-out;
    -moz-transition: 0.25s ease-in-out;
}

.all_filter_button {
    width: 15%;
}

.filter-button:hover,
.product_btns .active {
    background: linear-gradient( 0deg, rgba(116, 185, 110, 0.15) 0%, rgba(116, 185, 110, 0.018) 45.05%, rgba(116, 185, 110, 0) 74.14%);
    border-bottom: 1.5px solid rgba(116, 185, 110, 0.65);
    filter: drop-shadow(0px 0px 73px rgba(116, 185, 110, 0.05));
    border-radius: 25px;
}

.filter-button span {
    display: block;
    max-width: 30px;
    height: 2px;
    background: transparent;
    margin: 0px auto 5px;
    transition: 0.25s ease-in-out;
    -webkit-transition: 0.25s ease-in-out;
    -moz-transition: 0.25s ease-in-out;
}

.filter-button:hover span {
    background: rgba(116, 185, 110, 0.65);
}

.product_item {
    width: 32%;
    margin: 0% 0.65%;
    background: linear-gradient( 90deg, rgba(255, 255, 255, 0.07) 0%, rgba(255, 255, 255, 0) 80.34%);
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 10px;
    display: flex;
    backdrop-filter: blur(4px);
    align-items: center;
    justify-content: space-between;
}

.product_content {
    width: 55%;
    padding: 20px;
    padding-right: 0px;
}

.product_img {
    width: 42%;
}

.product_img img {
    height: 100%;
    width: 100%;
    margin-left: auto;
}

.product_content h4 {
    font-size: 22px;
    margin-bottom: 6px;
}

.product_content p {
    font-size: 15px;
    line-height: 1.4;
    margin-bottom: 12px;
}

.product_content button {
    height: 36px;
    width: 76px;
    font-size: 16px;
    background: rgba(116, 189, 101, 0.15);
    border-radius: 8px;
    transition: 0.25s ease-in-out;
    -webkit-transition: 0.25s ease-in-out;
    -moz-transition: 0.25s ease-in-out;
}

.product_content button:hover {
    background: rgba(116, 189, 101, 0.3);
}

@media screen and (max-width: 1400px) {
    .container_box {
        padding: 0px 100px;
    }
    .cart_item_box .side_title {
        left: -180px;
    }
    .logo_box {
        padding-bottom: 90px;
    }
    .filter-button {
        font-size: 14px;
        padding-left: 10px;
        padding-right: 10px;
    }
    .product_btns {
        padding-bottom: 40px;
    }
}

@media screen and (max-width: 1300px) {
    .container_box {
        padding: 0px 100px;
    }
    .cart_item_box .side_title {
        left: -180px;
    }
    .logo_box {
        padding-bottom: 90px;
    }
    .product_item {
        width: 48%;
    }
    .filter-button {
        font-size: 12px;
        padding-left: 10px;
        padding-right: 10px;
    }
    .product_btns {
        padding-bottom: 40px;
    }
}