local QBCore = exports['qb-core']:GetCoreObject()

local function getFrameworkPlayerBalance(xPlayer)
    if xPlayer then
        return xPlayer.PlayerData.money["cash"] or 0
    end
    return 0
end

local function removeFrameworkPlayerMoney(xPlayer, amount)
    if xPlayer then
        xPlayer.Functions.RemoveMoney("cash", amount)
    end
end

local function canAfford(source, cb, totalPrice, shoppingCart)
    local xPlayer = QBCore.Functions.GetPlayer(source)
    local playerBalance = getFrameworkPlayerBalance(xPlayer)
    totalPrice = tonumber(totalPrice)

    if playerBalance and playerBalance >= totalPrice then
        -- اضافه کردن آیتم‌ها به ps-inventory
        for itemName, itemData in pairs(shoppingCart) do
            local amount = itemData.amount or 1
            xPlayer.Functions.AddItem(itemName, amount)
        end

        removeFrameworkPlayerMoney(xPlayer, totalPrice)
        cb(true)
    else
        cb(false)
    end
end

QBCore.Functions.CreateCallback('xC_ShopSystem:canAfford', canAfford)



